using System.Collections.Generic;
using UnityEngine;
using FairyGUI;

/// <summary>
/// 简化的操作层接口，封装复杂的层级管理逻辑和网格显示功能
/// </summary>
public class OperationLayer
{
    private SmartLayerManager layerManager;
    private JigsawPanel parentPanel;
    private GComponent rootContainer;
    private GComponent operationComponent;
    
    // 网格配置
    public int gridColumns = 6;
    public int gridRows = 8;
    public Color gridLineColor = Color.white;
    public float gridLineWidth = 1f;
    public bool showGrid = true;
    private GComponent layerGrid;
    
    public OperationLayer(GComponent opComponent, JigsawPanel panel, GComponent container)
    {
        operationComponent = opComponent;
        parentPanel = panel;
        rootContainer = container;
        
        // 初始化网格层
        layerGrid = new GComponent();
        layerGrid.touchable = false;
        operationComponent.AddChildAt(layerGrid, 1);
        
        // 初始化管理器
        layerManager = new SmartLayerManager(container, panel);
        
        DoInitialize();
    }
    
    private void DoInitialize()
    {
        // 设置网格线颜色为半透明白色
        gridLineColor = new Color(1f, 1f, 1f, 0.3f);

        // 绘制网格
        if (showGrid)
        {
            DrawGrid();
        }
    }
    
    /// <summary>
    /// 拼块拖拽开始事件
    /// </summary>
    /// <param name="piece">拼块</param>
    public void OnPieceDragStart(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed || layerManager == null) return;
        
        try
        {
            // 注册拼块到层级管理系统（如果还未注册）
            Vector2Int gridPos = CalculateGridPosition(piece);
            layerManager.RegisterPiece(piece, gridPos);
            
            // 开始拖拽
            layerManager.StartDragging(piece);
        }
        catch (System.Exception ex)
        {
            UnityEngine.Debug.LogError($"OnPieceDragStart failed for piece {piece.pieceIndex}: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 拼块拖拽结束事件
    /// </summary>
    /// <param name="piece">拼块</param>
    public void OnPieceDragEnd(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed || layerManager == null) return;
        
        try
        {
            // 停止拖拽
            layerManager.StopDragging(piece);
        }
        catch (System.Exception ex)
        {
            UnityEngine.Debug.LogError($"OnPieceDragEnd failed for piece {piece.pieceIndex}: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 拼块位置变化事件
    /// </summary>
    /// <param name="piece">拼块</param>
    public void OnPiecePositionChanged(JigsawPiece piece)
    {
        if (piece == null || piece.isDisposed || layerManager == null) return;
        
        try
        {
            // 检查是否处于拖拽状态
            var layerInfo = layerManager.GetLayerInfo(piece);
            
            if (layerInfo.isDragging)
            {
                // 拖拽状态下，只更新thickness位置，不触碰层级逻辑
                var thickness = layerManager.GetThicknessFor(piece);
                if (thickness != null)
                {
                    layerManager.UpdateThicknessPositionOnly(piece, thickness);
                }
            }
            else
            {
                // 非拖拽状态下，正常更新层级和位置
                // layerManager.UpdatePiecePosition(piece);
                layerManager.UpdatePieceLayer(piece);
            }
        }
        catch (System.Exception ex)
        {
            UnityEngine.Debug.LogError($"OnPiecePositionChanged failed for piece {piece.pieceIndex}: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 添加拼块到层级管理
    /// </summary>
    /// <param name="piece">拼块</param>
    public void AddPieceToLayer(JigsawPiece piece)
    {
        if (piece == null) return;
        
        Vector2Int gridPos = CalculateGridPosition(piece);
        layerManager.RegisterPiece(piece, gridPos);
    }
    
    /// <summary>
    /// 从层级管理移除拼块
    /// </summary>
    /// <param name="piece">拼块</param>
    public void RemovePieceFromLayer(JigsawPiece piece)
    {
        if (piece == null) return;
        
        layerManager.UnregisterPiece(piece);
    }
    
    /// <summary>
    /// 更新拼块thickness位置
    /// </summary>
    /// <param name="piece">拼块</param>
    public void UpdatePieceThicknessPosition(JigsawPiece piece)
    {
        if (piece == null) return;
        
        // 直接调用位置变化事件，确保一致性
        OnPiecePositionChanged(piece);
    }
    
    /// <summary>
    /// 处理拖拽结束后的叠加逻辑
    /// </summary>
    /// <param name="piece">拼块</param>
    private void HandleStackingAfterDrop(JigsawPiece piece)
    {
        Vector2Int gridPos = CalculateGridPosition(piece);
        
        // 获取该网格位置的所有拼块
        var piecesAtGrid = GetPiecesAtGrid(gridPos);
        
        if (piecesAtGrid.Count > 1)
        {
            // 有多个拼块在同一位置，处理叠加
            var otherPieces = new List<JigsawPiece>(piecesAtGrid);
            otherPieces.Remove(piece);
        }
    }
    
    /// <summary>
    /// 获取指定网格位置的所有拼块
    /// </summary>
    /// <param name="gridPos">网格位置</param>
    /// <returns>拼块列表</returns>
    private List<JigsawPiece> GetPiecesAtGrid(Vector2Int gridPos)
    {
        // 这里需要从层级管理系统获取数据
        // 临时实现，需要完善
        var pieces = new List<JigsawPiece>();
        
        if (parentPanel != null)
        {
            var allPieces = parentPanel.GetPiecesInOperationLayer();
            foreach (var piece in allPieces)
            {
                if (CalculateGridPosition(piece) == gridPos)
                {
                    pieces.Add(piece);
                }
            }
        }
        
        return pieces;
    }
    
    /// <summary>
    /// 计算拼块的网格位置
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <returns>网格位置</returns>
    private Vector2Int CalculateGridPosition(JigsawPiece piece)
    {
        if (piece == null || parentPanel == null) return Vector2Int.zero;
        
        Vector2 centerOffset = new Vector2(piece.width * 0.5f, piece.height * 0.5f);
        Vector2 globalCenterPos = piece.LocalToGlobal(centerOffset);
        Vector2 operationLayerLocalPos = parentPanel.GlobalToOperationLayerLocal(globalCenterPos);
        
        return GetGridPosition(operationLayerLocalPos);
    }
    
    /// <summary>
    /// 获取层级管理器（用于高级操作）
    /// </summary>
    /// <returns>智能层级管理器</returns>
    public SmartLayerManager GetLayerManager()
    {
        return layerManager;
    }
    
    
    /// <summary>
    /// 清空所有层级管理数据
    /// </summary>
    public void Clear()
    {
        if (layerManager != null)
        {
            layerManager.Clear();
        }
    }
    
    /// <summary>
    /// 将拼块thickness移动到拖拽层（新系统中自动处理）
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="dragLayer">拖拽层</param>
    public void MovePieceThicknessToDragLayer(JigsawPiece piece, GComponent dragLayer)
    {
        // 新系统中拖拽状态通过层级自动处理
        OnPieceDragStart(piece);
    }
    
    /// <summary>
    /// 将拼块thickness从拖拽层移回操作层（新系统中自动处理）
    /// </summary>
    /// <param name="piece">拼块</param>
    /// <param name="dragLayer">拖拽层</param>
    public void MovePieceThicknessBackToLayer(JigsawPiece piece, GComponent dragLayer)
    {
        // 新系统中拖拽结束状态通过层级自动处理
        OnPieceDragEnd(piece);
    }
    
    // 网格显示相关方法
    
    /// <summary>
    /// 绘制网格线
    /// </summary>
    private void DrawGrid()
    {
        if (operationComponent == null) return;

        float cellWidth = operationComponent.width / gridColumns;
        float cellHeight = operationComponent.height / gridRows;

        // 绘制网格线
        DrawGridLines(cellWidth, cellHeight);
    }
    
    /// <summary>
    /// 绘制网格线（使用矩形边框方式）
    /// </summary>
    private void DrawGridLines(float cellWidth, float cellHeight)
    {
        // 绘制垂直线
        for (int i = 1; i < gridColumns; i++)
        {
            float x = i * cellWidth;
            var lineGraph = new GGraph();
            lineGraph.name = $"gridLine_v_{i}";
            lineGraph.SetSize(gridLineWidth, operationComponent.height);
            lineGraph.SetXY(x - gridLineWidth / 2, 0);
            lineGraph.DrawRect(gridLineWidth, operationComponent.height, 0, Color.clear, gridLineColor);
            
            layerGrid.AddChild(lineGraph);
        }

        // 绘制水平线
        for (int i = 1; i < gridRows; i++)
        {
            float y = i * cellHeight;
            var lineGraph = new GGraph();
            lineGraph.name = $"gridLine_h_{i}";
            lineGraph.SetSize(operationComponent.width, gridLineWidth);
            lineGraph.SetXY(0, y - gridLineWidth / 2);
            lineGraph.DrawRect(operationComponent.width, gridLineWidth, 0, Color.clear, gridLineColor);
            
            layerGrid.AddChild(lineGraph);
        }
    }
    
    /// <summary>
    /// 设置网格显示状态
    /// </summary>
    /// <param name="visible">是否显示网格</param>
    public void SetGridVisible(bool visible)
    {
        showGrid = visible;
        
        if (showGrid)
        {
            DrawGrid();
        }
        else
        {
            ClearGrid();
        }
    }
    
    /// <summary>
    /// 清除网格显示
    /// </summary>
    private void ClearGrid()
    {
        if (layerGrid == null) return;

        // 移除所有网格线
        for (int i = layerGrid.numChildren - 1; i >= 0; i--)
        {
            var child = layerGrid.GetChildAt(i);
            if (child.name != null && child.name.StartsWith("gridLine_"))
            {
                layerGrid.RemoveChildAt(i, true);
            }
        }
    }

    
    /// <summary>
    /// 获取指定位置对应的网格坐标
    /// </summary>
    /// <param name="localPosition">本地坐标</param>
    /// <returns>网格坐标</returns>
    public Vector2Int GetGridPosition(Vector2 localPosition)
    {
        if (operationComponent == null) return Vector2Int.zero;

        float cellWidth = operationComponent.width / gridColumns;
        float cellHeight = operationComponent.height / gridRows;

        int gridX = Mathf.FloorToInt(localPosition.x / cellWidth);
        int gridY = Mathf.FloorToInt(localPosition.y / cellHeight);

        // 限制在网格范围内
        gridX = Mathf.Clamp(gridX, 0, gridColumns - 1);
        gridY = Mathf.Clamp(gridY, 0, gridRows - 1);

        return new Vector2Int(gridX, gridY);
    }

    /// <summary>
    /// 获取网格坐标对应的本地位置（网格中心点）
    /// </summary>
    /// <param name="gridPosition">网格坐标</param>
    /// <returns>本地坐标</returns>
    public Vector2 GetLocalPosition(Vector2Int gridPosition)
    {
        if (operationComponent == null) return Vector2.zero;

        float cellWidth = operationComponent.width / gridColumns;
        float cellHeight = operationComponent.height / gridRows;

        float x = (gridPosition.x + 0.5f) * cellWidth;
        float y = (gridPosition.y + 0.5f) * cellHeight;

        return new Vector2(x, y);
    }
}